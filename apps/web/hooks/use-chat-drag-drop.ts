"use client";

import { useState, useCallback } from 'react';
import { DragEndEvent, DragStartEvent, DragOverEvent } from '@dnd-kit/core';
import { updateChat } from '@/services/src/chat';
import toast from 'react-hot-toast';
import { useLanguage } from '@/lib/language-context';
interface UseChatDragDropProps {
  onChatMoved?: (chatId: string, newGroupId: string | null) => void;
  onServerRefresh?: () => Promise<void> | void;
}

export function useChatDragDrop({ onChatMoved, onServerRefresh }: UseChatDragDropProps = {}) {
  const { t } = useLanguage();
  const [activeId, setActiveId] = useState<string | null>(null);
  const [overId, setOverId] = useState<string | null>(null);

  const handleDragStart = useCallback((event: DragStartEvent) => {
    setActiveId(event.active.id as string);
  }, []);

  const handleDragOver = useCallback((event: DragOverEvent) => {
    setOverId(event.over?.id as string || null);
  }, []);

  const handleDragEnd = useCallback(async (event: DragEndEvent) => {
    const { active, over } = event;

    setActiveId(null);
    setOverId(null);

    if (!over || !active.data.current) {
      console.log('Drag ended without valid drop target');
      toast.dismiss();
      return;
    }

    const dragData = active.data.current;
    const dropData = over.data.current;

    // Only handle chat to group drops (including ungrouping)
    if (dragData.type !== 'chat' || (dropData?.type !== 'group' && dropData?.type !== 'chat-history')) {
      console.log('Invalid drag operation:', { dragType: dragData.type, dropType: dropData?.type });
      toast.dismiss();
      return;
    }

    const chatId = dragData.chatId;
    const sourceGroupId = dragData.sourceGroupId;
    const targetGroupId = dropData?.type === 'chat-history' ? null : dropData.groupId;

    // Don't move if dropping in the same location
    if (sourceGroupId === targetGroupId) {
      console.log('Chat dropped in same location, no action needed');
      toast.dismiss();
      return;
    }

    console.log('Starting chat move operation:', {
      chatId,
      sourceGroupId,
      targetGroupId,
      operation: targetGroupId ? 'move to group' : 'ungroup'
    });

    // Store original state for potential rollback
    const originalState = { chatId, sourceGroupId };

    let loadingToastId: string | undefined;
    try {
      // Show loading state
      loadingToastId = toast.loading(t("chat.moving") || "Moving chat...");

      // Perform optimistic update first
      onChatMoved?.(chatId, targetGroupId);
      console.log('Optimistic update applied');

      // Make API call
      console.log('Making API call to update chat...');
      const response = await updateChat({
        id: chatId,
        groupId: targetGroupId,
      });

      console.log('API response received:', response);
      toast.dismiss(loadingToastId);

      if (response.error) {
        console.error('API returned error:', response.error);
        toast.error(response.error);

        // Rollback optimistic update
        console.log('Rolling back optimistic update...');
        onChatMoved?.(chatId, originalState.sourceGroupId);
      } else {
        console.log('Chat move successful, refreshing server state...');
        const successMessage = targetGroupId
          ? (t("chat.moveSuccess") || "Chat moved successfully")
          : (t("chat.ungroupSuccess") || "Chat removed from group successfully");
        toast.success(successMessage);

        // Always refresh server state to ensure consistency
        try {
          await onServerRefresh?.();
          console.log('Server refresh completed successfully');
        } catch (refreshError) {
          console.error('Server refresh failed:', refreshError);
          toast.error(t("chat.refreshFailed") || "Failed to refresh chat data");
        }
      }
    } catch (error) {
      console.error('Error during chat move operation:', error);
      toast.dismiss(loadingToastId);
      toast.error(t("chat.moveFailed") || "Failed to move chat");

      // Rollback optimistic update
      console.log('Rolling back optimistic update due to error...');
      onChatMoved?.(chatId, originalState.sourceGroupId);
    }
  }, [onChatMoved, onServerRefresh, t]);

  return {
    activeId,
    handleDragStart,
    handleDragOver,
    handleDragEnd,
  };
}
