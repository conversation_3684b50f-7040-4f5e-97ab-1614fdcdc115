"use client";

import { useState, useCallback } from 'react';
import { DragEndEvent, DragStartEvent, DragOverEvent } from '@dnd-kit/core';
import { updateChat } from '@/services/src/chat';
import toast from 'react-hot-toast';
import { useLanguage } from '@/lib/language-context';

interface UseChatDragDropProps {
  onChatMoved?: (chatId: string, newGroupId: string) => void;
}

export function useChatDragDrop({ onChatMoved }: UseChatDragDropProps = {}) {
  const { t } = useLanguage();
  const [activeId, setActiveId] = useState<string | null>(null);
  const [overId, setOverId] = useState<string | null>(null);

  const handleDragStart = useCallback((event: DragStartEvent) => {
    setActiveId(event.active.id as string);
  }, []);

  const handleDragOver = useCallback((event: DragOverEvent) => {
    setOverId(event.over?.id as string || null);
  }, []);

  const handleDragEnd = useCallback(async (event: DragEndEvent) => {
    const { active, over } = event;
    
    setActiveId(null);
    setOverId(null);

    if (!over || !active.data.current) {
      return;
    }

    const dragData = active.data.current;
    const dropData = over.data.current;

    // Only handle chat to group drops
    if (dragData.type !== 'chat' || dropData?.type !== 'group') {
      return;
    }

    const chatId = dragData.chatId;
    const sourceGroupId = dragData.sourceGroupId;
    const targetGroupId = dropData.groupId;

    // Don't move if dropping in the same group
    if (sourceGroupId === targetGroupId) {
      return;
    }

    try {
      toast.loading(t("chat.moving") || "Moving chat...");
      
      const response = await updateChat({
        id: chatId,
        groupId: targetGroupId,
      });

      toast.dismiss();

      if (response.error) {
        toast.error(response.error);
      } else {
        toast.success(t("chat.moveSuccess") || "Chat moved successfully");
        onChatMoved?.(chatId, targetGroupId);
        
        // Reload the page to refresh the sidebar
        window.location.reload();
      }
    } catch (error) {
      toast.dismiss();
      toast.error(t("chat.moveFailed") || "Failed to move chat");
      console.error('Error moving chat:', error);
    }
  }, [onChatMoved, t]);

  return {
    activeId,
    overId,
    handleDragStart,
    handleDragOver,
    handleDragEnd,
  };
}
