export interface ChatItem {
  id: string;
  title: string;
  url?: string;
  groupId?: string;
  createdAt?: string;
  updatedAt?: string;
}

export interface GroupItem {
  id: string;
  name: string;
  chats?: ChatItem[];
}

export interface DragData {
  type: 'chat';
  chatId: string;
  sourceGroupId?: string;
  chatTitle: string;
}

export interface DropResult {
  targetGroupId: string;
  insertIndex?: number;
}

export interface DragState {
  isDragging: boolean;
  draggedItem: DragData | null;
  dragOverGroupId: string | null;
  dragOverPosition: 'top' | 'bottom' | 'center' | null;
}

export interface ChatDragContextValue {
  dragState: DragState;
  onDragStart: (data: DragData) => void;
  onDragEnd: () => void;
  onDragOver: (groupId: string, position?: 'top' | 'bottom' | 'center') => void;
  onDrop: (result: DropResult) => Promise<void>;
}
