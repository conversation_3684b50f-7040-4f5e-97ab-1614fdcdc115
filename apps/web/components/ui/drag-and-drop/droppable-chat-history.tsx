"use client";

import React from 'react';
import { useDroppable } from '@dnd-kit/core';
import { cn } from '@/lib/utils';
import { useLanguage } from '@/lib/language-context';

interface DroppableChatHistoryProps {
  children: React.ReactNode;
  className?: string;
}

export function DroppableChatHistory({
  children,
  className,
}: DroppableChatHistoryProps) {
  const { t } = useLanguage();
  
  const {
    isOver,
    setNodeRef,
  } = useDroppable({
    id: 'chat-history',
    data: {
      type: 'chat-history',
    },
  });

  return (
    <div
      ref={setNodeRef}
      className={cn(
        "relative transition-all duration-200",
        isOver && "bg-primary/5 border border-primary/30 rounded-md",
        className
      )}
    >
      {children}
      
      {/* Drop indicator for chat history */}
      {isOver && (
        <div className="absolute inset-0 flex items-center justify-center bg-primary/5 rounded-md border-2 border-dashed border-primary/40 pointer-events-none z-10">
          <div className="text-xs text-primary font-medium px-2 py-1 bg-background/90 rounded shadow-sm">
            {t("chat.dropToUngroup") || "Drop here to remove from group"}
          </div>
        </div>
      )}
    </div>
  );
}
