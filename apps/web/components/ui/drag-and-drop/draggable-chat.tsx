"use client";

import React from 'react';
import { useDraggable } from '@dnd-kit/core';
import { CSS } from '@dnd-kit/utilities';
import { ChatItem } from './types';
import { cn } from '@/lib/utils';
import { GripVertical } from 'lucide-react';

interface DraggableChatProps {
  chat: ChatItem;
  children: React.ReactNode;
  className?: string;
}

interface DragHandleProps {
  listeners: any;
  attributes: any;
  isDragging: boolean;
}

function DragHandle({ listeners, attributes, isDragging }: DragHandleProps) {
  return (
    <div
      className={cn(
        "absolute left-0 top-1/2 -translate-y-1/2 w-3 h-6 flex items-center justify-center opacity-0 group-hover:opacity-60 hover:opacity-100 transition-opacity cursor-grab active:cursor-grabbing z-20 bg-background/80 rounded-sm",
        isDragging && "opacity-100"
      )}
      {...listeners}
      {...attributes}
      title="Drag to move chat"
    >
      <GripVertical className="h-3 w-3 text-muted-foreground" />
    </div>
  );
}

export function DraggableChat({ chat, children, className }: DraggableChatProps) {

  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    isDragging,
  } = useDraggable({
    id: `chat-${chat.id}`,
    data: {
      type: 'chat',
      chatId: chat.id,
      sourceGroupId: chat.groupId,
      chatTitle: chat.title,
    },
  });

  const style = {
    transform: CSS.Translate.toString(transform),
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={cn(
        "group relative",
        isDragging && "opacity-0", // Hide completely during drag
        className
      )}
    >
      <DragHandle
        listeners={listeners}
        attributes={attributes}
        isDragging={isDragging}
      />
      {children}
    </div>
  );
}
