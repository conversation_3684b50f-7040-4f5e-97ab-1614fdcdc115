"use client";

import React from 'react';
import { useDraggable } from '@dnd-kit/core';
import { CSS } from '@dnd-kit/utilities';
import { ChatItem } from './types';
import { useChatDrag } from './chat-drag-context';
import { cn } from '@/lib/utils';

interface DraggableChatProps {
  chat: ChatItem;
  children: React.ReactNode;
  className?: string;
}

export function DraggableChat({ chat, children, className }: DraggableChatProps) {
  const { onDragStart, onDragEnd } = useChatDrag();
  
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    isDragging,
  } = useDraggable({
    id: `chat-${chat.id}`,
    data: {
      type: 'chat',
      chatId: chat.id,
      sourceGroupId: chat.groupId,
      chatTitle: chat.title,
    },
  });

  const style = {
    transform: CSS.Translate.toString(transform),
  };

  React.useEffect(() => {
    if (isDragging) {
      onDragStart({
        type: 'chat',
        chatId: chat.id,
        sourceGroupId: chat.groupId,
        chatTitle: chat.title,
      });
    }
  }, [isDragging, chat.id, chat.groupId, chat.title, onDragStart]);

  React.useEffect(() => {
    if (!isDragging) {
      onDragEnd();
    }
  }, [isDragging, onDragEnd]);

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={cn(
        "transition-all duration-200",
        isDragging && "opacity-50 scale-95 z-50",
        className
      )}
      {...listeners}
      {...attributes}
    >
      {children}
    </div>
  );
}
