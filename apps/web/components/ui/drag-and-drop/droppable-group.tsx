"use client";

import React from 'react';
import { useDroppable } from '@dnd-kit/core';
import { GroupItem } from './types';
import { useChatDrag } from './chat-drag-context';
import { cn } from '@/lib/utils';
import { useLanguage } from '@/lib/language-context';

interface DroppableGroupProps {
  group: GroupItem;
  children: React.ReactNode;
  className?: string;
  isExpanded?: boolean;
}

export function DroppableGroup({
  group,
  children,
  className,
  isExpanded = false
}: DroppableGroupProps) {
  const { dragState, onDragOver, onDrop } = useChatDrag();
  const { t } = useLanguage();
  
  const {
    isOver,
    setNodeRef,
  } = useDroppable({
    id: `group-${group.id}`,
    data: {
      type: 'group',
      groupId: group.id,
    },
  });

  const isDraggedFromThisGroup = dragState.draggedItem?.sourceGroupId === group.id;
  const canDrop = dragState.isDragging && !isDraggedFromThisGroup;
  const isDropTarget = isOver && canDrop;

  React.useEffect(() => {
    if (isOver && dragState.isDragging) {
      onDragOver(group.id, 'center');
    }
  }, [isOver, dragState.isDragging, group.id, onDragOver]);

  const handleDrop = React.useCallback(() => {
    if (canDrop) {
      onDrop({
        targetGroupId: group.id,
      });
    }
  }, [canDrop, onDrop, group.id]);

  React.useEffect(() => {
    if (isDropTarget) {
      const handleDropEvent = () => handleDrop();
      // We'll handle the drop through the DndContext
      return () => {};
    }
  }, [isDropTarget, handleDrop]);

  return (
    <div
      ref={setNodeRef}
      className={cn(
        "transition-all duration-200",
        isDropTarget && isExpanded && "bg-primary/5 border-l-2 border-primary/30",
        isDropTarget && !isExpanded && "bg-primary/10 border border-primary/30 rounded-md",
        canDrop && "ring-1 ring-primary/20",
        className
      )}
    >
      {children}
      
      {/* Drop indicator for collapsed groups */}
      {isDropTarget && !isExpanded && (
        <div className="absolute inset-0 flex items-center justify-center bg-primary/5 rounded-md border-2 border-dashed border-primary/40 pointer-events-none">
          <div className="text-xs text-primary font-medium px-2 py-1 bg-background/80 rounded">
            {t("chat.dropHere")} {group.name}
          </div>
        </div>
      )}
    </div>
  );
}
