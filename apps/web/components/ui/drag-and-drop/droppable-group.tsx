"use client";

import React from 'react';
import { useDroppable } from '@dnd-kit/core';
import { GroupItem } from './types';
import { cn } from '@/lib/utils';
import { useLanguage } from '@/lib/language-context';

interface DroppableGroupProps {
  group: GroupItem;
  children: React.ReactNode;
  className?: string;
  isExpanded?: boolean;
}

export function DroppableGroup({
  group,
  children,
  className,
  isExpanded = false
}: DroppableGroupProps) {
  const { t } = useLanguage();
  
  const {
    isOver,
    setNodeRef,
  } = useDroppable({
    id: `group-${group.id}`,
    data: {
      type: 'group',
      groupId: group.id,
    },
  });

  return (
    <div
      ref={setNodeRef}
      className={cn(
        "transition-all duration-200",
        isOver && isExpanded && "bg-primary/5 border-l-2 border-primary/30",
        isOver && !isExpanded && "bg-primary/10 border border-primary/30 rounded-md",
        className
      )}
    >
      {children}

      {/* Drop indicator for collapsed groups */}
      {isOver && !isExpanded && (
        <div className="absolute inset-0 flex items-center justify-center bg-primary/5 rounded-md border-2 border-dashed border-primary/40 pointer-events-none">
          <div className="text-xs text-primary font-medium px-2 py-1 bg-background/80 rounded">
            {t("chat.dropHere")} {group.name}
          </div>
        </div>
      )}
    </div>
  );
}
