"use client";

import React from 'react';
import { useDroppable } from '@dnd-kit/core';
import { GroupItem } from './types';
import { cn } from '@/lib/utils';
import { useLanguage } from '@/lib/language-context';

interface DroppableGroupProps {
  group: GroupItem;
  children: React.ReactNode;
  className?: string;
  isExpanded?: boolean;
  isDragging?: boolean;
}

export function DroppableGroup({
  group,
  children,
  className,
  isExpanded = false,
  isDragging = false
}: DroppableGroupProps) {
  const { t } = useLanguage();
  
  const {
    isOver,
    setNodeRef,
  } = useDroppable({
    id: `group-${group.id}`,
    data: {
      type: 'group',
      groupId: group.id,
    },
  });

  // Debug logging for drop state
  React.useEffect(() => {
    console.log(`🎯 DroppableGroup ${group.id} (${group.name}):`, { isOver, isExpanded, isDragging });
  }, [isOver, isExpanded, isDragging, group.id, group.name]);

  return (
    <div
      ref={setNodeRef}
      className={cn(
        "transition-all duration-200",
        isOver && isExpanded && "bg-transparent border",
        isOver && !isExpanded && "bg-transparent border",
        className
      )}
      data-droppable-id={`group-${group.id}`}
    >
      {children}

      {/* Drop indicator for collapsed groups */}
      {isOver && !isExpanded && isDragging && (
        <div className="absolute inset-0 flex items-center justify-center bg-transparent rounded-md border pointer-events-none z-10">
          <div className="text-xs text-green-700 font-medium px-2 py-1 border bg-transparent rounded shadow-sm">
            {t("chat.dropHere") || "Drop here:"} {group.name}
          </div>
        </div>
      )}

      {/* Drop indicator for expanded groups */}
      {isOver && isExpanded && isDragging && (
        <div className="absolute left-0 top-0 bottom-0 w-1 bg-transparent pointer-events-none z-10"></div>
      )}
    </div>
  );
}
