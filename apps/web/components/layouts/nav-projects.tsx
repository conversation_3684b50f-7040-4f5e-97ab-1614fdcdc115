"use client";

import { useState } from "react";
import {
  MoreHorizontal,
  Pen<PERSON>l,
  Folder,
  Trash2,
  type LucideIcon,
} from "lucide-react";
import { updateChat, deleteChat } from "@/services/src/chat";
import toast from "react-hot-toast";
import { useLanguage } from "@/lib/language-context";
import Link from "next/link";

import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem,
} from "@/components/ui/dropdown-menu";

import {
  SidebarGroup,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/layouts/sidebar";
import { DropdownMenuSeparator } from "@radix-ui/react-dropdown-menu";

import { MoveToGroupDialog } from "@/components/model/move-to-group-dialog";
import { usePathname, useRouter } from "next/navigation";
import { Input } from "../ui/input";
import { DraggableChat } from "@/components/ui/drag-and-drop";

export function NavProjects({
  projects,
  showMore = false,
  onRename,
  onMoveToGroup,
  onRemove,
  groups,
}: {
  showMore?: boolean;
  groups?: any;
  projects: {
    name: string;
    url: string;
    icon: LucideIcon;
    translationKey?: string;
  }[];
  onRename?: (chatId: string, newName: string) => void;
  onMoveToGroup?: (chatId: string, groupId: string) => void;
  onRemove?: (chatId: string) => void;
}) {
  const router = useRouter();
  const [hoveredProject, setHoveredProject] = useState<number | null>(null);
  const [openDropdown, setOpenDropdown] = useState<number | null>(null);
  const [editingIndex, setEditingIndex] = useState<number | null>(null);
  const [editingName, setEditingName] = useState("");
  const pathname = usePathname();
  const { t } = useLanguage();
  const handleStartEditing = (index: number, name: string) => {
    setEditingIndex(index);
    setEditingName(name);
    setOpenDropdown(null);
  };

  const handleNameSubmit = async (item: any) => {
    if (editingName.trim() === "") return;

    toast.loading(t("chat.renaming"));
    try {
      const response = await updateChat({
        id: item.id,
        title: editingName,
      });
      toast.remove();
      if (response.error) {
        toast.error(response.error);
      } else {
        toast.success(t("chat.renameSuccess"));
        onRename?.(item.id, editingName);
        router.refresh();
      }
    } catch (error) {
      toast.error(t("chat.renameFailed"));
    } finally {
      setEditingIndex(null);
    }
  };

  return (
    <SidebarGroup className="group-data-[collapsible=icon]:hidden ">
      <SidebarMenu>
        {projects.map((item: any, index) => {
          // Only wrap chat items (those with an id) with DraggableChat
          const isChatItem = item.id && item.name;

          const menuItem = (
            <SidebarMenuItem
              key={index}
              className="relative flex items-center justify-between"
              onMouseEnter={() => {
                setHoveredProject(index);
              }}
              onMouseLeave={() => setHoveredProject(null)}
            >
            {/* Left Side - Project Link */}
            <SidebarMenuButton asChild>
              {editingIndex === index ? (
                <div className="flex items-center gap-2 w-full">
                  {item?.icon && <item.icon />}
                  <input
                    type="text"
                    value={editingName}
                    onChange={(e) => setEditingName(e.target.value)}
                    onBlur={() => handleNameSubmit(item)}
                    onKeyDown={(e) => {
                      if (e.key === "Enter") {
                        handleNameSubmit(item);
                      } else if (e.key === "Escape") {
                        setEditingIndex(null);
                      }
                    }}
                    className="bg-transparent border-none outline-none w-full max-w-[185px] text-sm focus:ring-2 focus:ring-primary focus-visible:ring-2 focus-visible:ring-primary"
                    autoFocus={true}
                  />
                </div>
              ) : (
                <Link
                  href={item.url}
                  className={`flex items-center gap-2 w-full ${
                    pathname === item.url
                      ? "bg-sidebar-accent text-sidebar-accent-foreground"
                      : ""
                  }`}
                >
                  {item?.icon && <item.icon />}
                  <span className="truncate whitespace-nowrap overflow-hidden max-w-[185px]">
                    {item.translationKey ? t(item.translationKey) : item.name}
                  </span>
                </Link>
              )}
            </SidebarMenuButton>

            {/* Right Side - More Options (Only on Hover) */}

            <GroupMenuItem
              index={index}
              item={item}
              openDropdown={openDropdown}
              setOpenDropdown={setOpenDropdown}
              hoveredProject={hoveredProject}
              showMore={showMore}
              groups={groups}
              handleStartEditing={handleStartEditing}
              onMoveToGroup={onMoveToGroup}
              onRemove={onRemove}
              router={router}
            />
          </SidebarMenuItem>
          );

          // Return wrapped or unwrapped based on whether it's a chat item
          return isChatItem ? (
            <DraggableChat
              key={item.id}
              chat={{
                id: item.id,
                title: item.name,
                url: item.url,
                groupId: undefined, // Ungrouped chats
              }}
            >
              {menuItem}
            </DraggableChat>
          ) : (
            menuItem
          );
        })}
      </SidebarMenu>
    </SidebarGroup>
  );
}

export const GroupMenuItem = ({
  index,
  item,
  openDropdown,
  setOpenDropdown,
  hoveredProject,
  showMore,
  groups,
  handleStartEditing,
  onMoveToGroup,
  onRemove,
  router,
}: {
  index: number;
  item: any;
  openDropdown: number | null;
  setOpenDropdown: (index: number | null) => void;
  hoveredProject: number | null;
  showMore: boolean;
  groups: any;
  handleStartEditing: (index: number, name: string) => void;
  onMoveToGroup?: (chatId: string, groupId: string) => void;
  onRemove?: (chatId: string) => void;
  router: any;
}) => {
  const { t } = useLanguage();
  return (
    <DropdownMenu
      open={openDropdown === index}
      onOpenChange={(open) => setOpenDropdown(open ? index : null)}
    >
      <DropdownMenuTrigger asChild onClick={() => setOpenDropdown(index)}>
        <button
          className="p-1 rounded-md hover:bg-gray-200 dark:hover:bg-gray-700 transition"
          onClick={(e) => {
            e.preventDefault();
            e.stopPropagation();
          }}
        >
          {(hoveredProject === index || openDropdown === index) && showMore && (
            <MoreHorizontal className="h-5 w-5" />
          )}
        </button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="center" className="">
        <DropdownMenuItem
          onSelect={(e) => {
            e.preventDefault();
            e.stopPropagation();
            handleStartEditing(index, item.name);
          }}
        >
          <Pencil className="h-4 w-4 mr-2" /> {t("common.rename")}
        </DropdownMenuItem>

        <MoveToGroupDialog
          groups={groups}
          chatId={item.id}
          currentGroupId={item?.groupId}
          onMoveToGroup={async (id, groupId) => {
            if (groupId) {
              toast.loading(t("chat.moving"));
              try {
                const response = await updateChat({
                  id,
                  groupId,
                });
                toast.remove();
                if (response.error) {
                  toast.error(response.error);
                } else {
                  toast.success(t("chat.moveSuccess"));
                  onMoveToGroup?.(item?.id, groupId);
                  window.location.reload();
                }
              } catch (error) {
                toast.error(t("chat.moveFailed"));
              }
            }
          }}
          trigger={
            <DropdownMenuItem onSelect={(e) => e.preventDefault()}>
              <Folder className="h-4 w-4 mr-2" /> {t("chat.moveToGroup")}
            </DropdownMenuItem>
          }
        />
        <DropdownMenuSeparator />
        <DropdownMenuItem
          onSelect={(e) => {
            setOpenDropdown(null);
            e.preventDefault();
            e.stopPropagation();
          }}
          onClick={async () => {
            try {
              toast.loading(t("chat.removing"));
              const response = await deleteChat(item?.id);
              toast.remove();
              if (response.error) {
                toast.error(response.error);
              } else {
                toast.success(t("chat.removeSuccess"));
                router.push("/ask-ai");
                window.location.reload();
                onRemove?.(item.id);
              }
            } catch (error) {
              toast.error(t("chat.removeFailed"));
            }
          }}
          className="text-red-500"
        >
          <Trash2 className="h-4 w-4 mr-2" /> {t("common.remove")}
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};
