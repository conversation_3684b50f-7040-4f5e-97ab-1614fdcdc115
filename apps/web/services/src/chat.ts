import { apiUrl, fetchJson } from "..";

export const createChat = async (data) => {
  try {
    const response = await fetch(`${apiUrl}/chat/id`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      throw new Error("Network response was not ok");
    }
    return await response.json();
  } catch (error) {
    console.log({ "Create Chat api calling error": error });
    return { error: "Error Creating The Chat" };
  }
};

export const updateChat = async (data) => {
  try {
    console.log('updateChat service called with data:', data);

    // Use dynamic API URL to handle port differences
    const baseUrl = typeof window !== 'undefined' ? window.location.origin : apiUrl.replace('/api', '');
    const fullApiUrl = `${baseUrl}/api/chat/${data.id}`;

    console.log('Making API call to:', fullApiUrl);

    const response = await fetch(fullApiUrl, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    });

    console.log('updateChat API response status:', response.status);

    if (!response.ok) {
      const errorData = await response.text();
      console.error('updateChat API error response:', errorData);
      throw new Error(`API Error: ${response.status} - ${errorData}`);
    }

    const result = await response.json();
    console.log('updateChat API success response:', result);
    return result;
  } catch (error) {
    console.error('updateChat service error:', error);
    return {
      error: error instanceof Error ? error.message : "Error updating chat",
      details: error
    };
  }
};

export const getChat = async ({ id = "", userId, tenantId }) => {
  try {
    if (id) {
      const response = await fetchJson(
        `${apiUrl}/chat/${id}?id=${id}&userId=${userId}&tenantId=${tenantId}`
      );

      return response;
    }
    const response = await fetchJson(
      `${apiUrl}/chat/id?userId=${userId}&tenantId=${tenantId}`
    );

    return response;
  } catch (error) {
    console.error("Error in getChat service:", error);
    throw error;
  }
};

export const deleteChat = async (id) => {
  try {
    const url = `${apiUrl}/chat/${id}?id=${id}`;
    const response = await fetch(url, {
      method: "DELETE",
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || "Network response was not ok");
    }
    return await response.json();
  } catch (error) {
    console.log({ "delete Chat api error": error });
    return { error: error.message || "Error deleting Chat" };
  }
};
